import { attachPersonaToJob, getPersonas } from '@/services/persona';
import { useMutation, useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { toast } from 'sonner';

export const personaKeys = {
  all: ['persona'] as const,
};

export function usePersonasQuery(options = { enabled: true }) {
  return useQuery({
    queryKey: personaKeys.all,
    queryFn: async () => {
      const response = await getPersonas();
      return response.data;
    },
    enabled: options.enabled,
    staleTime: 5 * 60 * 1000,
  });
}

export function useAttachPersonaToJobMutation() {
  return useMutation({
    mutationFn: async (payload: { jobId: string; list: { personas: JobPersona[] } }) => {
      const response = await attachPersonaToJob(payload);
      return response.data;
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to attach persona to job';
        toast.error(errorMessage);
      }
    },
  });
}
