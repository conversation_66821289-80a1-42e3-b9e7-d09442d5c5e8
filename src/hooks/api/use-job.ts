import {createJob, getJob<PERSON>, getJob, getActive<PERSON>ob<PERSON>, getJob<PERSON><PERSON><PERSON>, getJobById, getGenerateJobDescription, updateJob} from '@/services/job';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { toast } from 'sonner';

export const jobKeys = {
  all: ['job'] as const,
  lists: () => [...jobKeys.all, 'list'] as const,
  list: (filters: string) => [...jobKeys.lists(), { filters }] as const,
  details: () => [...jobKeys.all, 'detail'] as const,
  detail: (id: string) => [...jobKeys.details(), id] as const,
};

export function useCreateJobMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['create-job'],
    mutationFn: async (payload: JobInformation) => {
      const response = await createJob(payload);
      return response.data;
    },
    onSuccess: (_data) => {
      queryClient.invalidateQueries({ queryKey: jobKeys.lists() });
      toast.success('Job created successfully!');
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to create job';
        toast.error(errorMessage);
      }
    },
  });
}

export function useJobsQuery(options = { enabled: true }) {
  return useQuery({
    queryKey: jobKeys.lists(),
    queryFn: async () => {
      const response = await getJobs();
      return response.data;
    },
    enabled: options.enabled,
    staleTime: 5 * 60 * 1000,
  });
}

export function useJobByIdQuery(id: string, options = { enabled: true }) {
  return useQuery({
    queryKey: jobKeys.detail(id),
    queryFn: async () => {
      const response = await getJobById(id);
      return response.data;
    },
    enabled: options.enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useActiveJobsQuery(options = { enabled: true }) {
  return useQuery({
    queryKey: jobKeys.lists(),
    queryFn: async () => {
      const response = await getActiveJobs();
      return response.data;
    },
    enabled: options.enabled,
    staleTime: 5 * 60 * 1000,
  });
}

export function useJobQuery(id: string, options = { enabled: true }) {
  return useQuery({
    queryKey: jobKeys.detail(id),
    queryFn: async () => {
      const response = await getJob(id);
      return response.data;
    },
    enabled: options.enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export const useUpdateJobMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['update-job'],
    mutationFn: async ({ id, payload }: { id: string; payload: JobInformation }) => {
      const response = await updateJob(id, payload);
      return response.data;
    },
    onSuccess: (_data) => {
      queryClient.invalidateQueries({ queryKey: jobKeys.lists() });
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to update job';
        toast.error(errorMessage);
      }
    },
  });
};

export function useGenerateJobDescription(id: string, options = { enabled: false }) {
  return useQuery({
    queryKey: ['generate-job-description', id],
    queryFn: async () => {
      const response = await getGenerateJobDescription(id);
      return response.data;
    },
    enabled: options.enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useJobRolesQuery(options = { enabled: true }) {
  return useQuery({
    queryKey: ['job-roles'],
    queryFn: async () => {
      const response = await getJobRoles();
      return response.data;
    },
    enabled: options.enabled,
    staleTime: 5 * 60 * 1000,
  });
}