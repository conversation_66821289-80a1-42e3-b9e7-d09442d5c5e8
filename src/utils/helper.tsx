export function getCapitalizedText(text: string): string {
  if (!text) return text;
  return text?.charAt(0)?.toUpperCase() + text?.slice(1).toLowerCase();
}

export const getValuesFromOptions = (options: SelectProp[]) => {
  if (!options || options.length === 0) return [];

  return options.map((option: SelectProp) => option.value);
};

export function getInitials(name: string, maxLength: number = 2, fallback: string = 'AP'): string {
  // Handle null, undefined, or empty string
  if (!name || typeof name !== 'string') {
    return fallback;
  }

  // Clean the name: trim whitespace and remove extra spaces
  const cleanName = name.trim().replace(/\s+/g, ' ');

  if (!cleanName) {
    return fallback;
  }

  // Split by spaces and filter out empty strings
  const words = cleanName.split(' ').filter((word) => word.length > 0);

  if (words.length === 0) {
    return fallback;
  }

  // Generate initials from each word
  const initials = words
    ?.slice(0, maxLength) // Limit to maxLength words
    ?.map((word) => {
      // Get first character that is a letter
      const firstLetter = word.match(/[a-zA-Z]/);
      return firstLetter ? firstLetter[0].toUpperCase() : '';
    })
    ?.filter((initial) => initial.length > 0) // Remove empty initials
    ?.join('');

  // If no valid initials found, return fallback
  if (!initials) {
    return fallback;
  }

  return initials?.slice(0, maxLength);
}

export function getFormatDateString(dateString: string | null | undefined): string {
  if (!dateString) return 'N/A';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'N/A';

    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  } catch {
    return 'N/A';
  }
}

export function getTimeLeft(
  deadline: string | Date | null | undefined,
  fallback: string = 'N/A'
): string {
  if (!deadline) return fallback;

  let deadlineDate: Date;
  if (deadline instanceof Date) {
    deadlineDate = deadline;
  } else {
    deadlineDate = new Date(deadline);
  }

  if (isNaN(deadlineDate.getTime())) return fallback;

  const now = new Date();
  // If deadline is in the past
  if (deadlineDate.getTime() <= now.getTime()) return 'Expired';

  // Calculate difference in days, hours, minutes
  const diffMs = deadlineDate.getTime() - now.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor((diffMs / (1000 * 60 * 60)) % 24);
  const diffMinutes = Math.floor((diffMs / (1000 * 60)) % 60);

  if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? 's' : ''}`;
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours > 1 ? 's' : ''}`;
  } else if (diffMinutes > 0) {
    return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
  } else {
    return 'Less than a minute';
  }
}

export const getFormatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes === 0) {
    return `${remainingSeconds} sec`;
  }

  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')} min`;
};

export const fileSizeValidation = (files: File[], maxFileSize: number) => {
  const MAX_FILE_SIZE_BYTES = maxFileSize * 1024 * 1024;
  return files.every((file) => file.size <= MAX_FILE_SIZE_BYTES);
};

export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export function getDaysLeft(
  startDate: string | Date | null | undefined,
  endDate: string | Date | null | undefined,
  unitDisplay: string = 'days',
  fallback: string = 'N/A'
): string {
  if (!startDate || !endDate) return fallback;

  let start: Date;
  let end: Date;

  try {
    start = startDate instanceof Date ? startDate : new Date(startDate);
    end = endDate instanceof Date ? endDate : new Date(endDate);
  } catch {
    return fallback;
  }

  if (isNaN(start.getTime()) || isNaN(end.getTime())) return fallback;

  const now = new Date();

  if (now < start) {
    const diffMs = start.getTime() - now.getTime();
    const daysUntilStart = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    return `${daysUntilStart} ${unitDisplay}`;
  }

  if (now > end) {
    return `0 ${unitDisplay}`;
  }

  const diffMs = end.getTime() - now.getTime();
  const daysLeft = Math.ceil(diffMs / (1000 * 60 * 60 * 24));

  if (daysLeft === 0) {
    return `Last ${unitDisplay}`;
  }

  return `${daysLeft} ${unitDisplay}`;
}
