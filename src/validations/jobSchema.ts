import { z } from 'zod';

const today = new Date();
today.setHours(0, 0, 0, 0);

const jobInfoSchema = z
  .object({
    title: z.string().min(1, 'Title is required').max(55, 'Title is too long'),
    description: z.string().optional(),
    location: z.string().optional(),
    initial_filter_criteria: z.string().optional(),
    job_role_id: z.string().min(1, 'Position is required'),
    min_exp: z
      .string()
      .min(1, 'Minimum experience is required')
      .regex(/^\d+$/, 'Minimum experience must be a positive whole number'),
    max_exp: z
      .string()
      .min(1, 'Maximum experience is required')
      .regex(/^\d+$/, 'Maximum experience must be a positive whole number'),
    application_start_date: z
      .date({
        required_error: 'Date is required',
        invalid_type_error: 'Invalid date',
      })
      .min(today, 'Starting date must be today or in the future'),
    application_end_date: z
      .date({
        required_error: 'Date is required',
        invalid_type_error: 'Invalid date',
      })
      .min(today, 'Application deadline must be in the future'),
  })
  .refine(
    (data) => {
      if (data.min_exp !== undefined && data.max_exp !== undefined) {
        return data.max_exp > data.min_exp;
      }
      return true;
    },
    {
      message: 'Maximum experience must be greater than minimum experience',
      path: ['max_exp'],
    }
  )
  .refine(
    (data) => {
      if (data.min_exp || data.max_exp) {
        return /^-?\d+$/.test(data.min_exp) && /^-?\d+$/.test(data.max_exp);
      }
      return true;
    },
    {
      message: 'Please enter a whole number only (e.g., 1, 3)',
    }
  )
  .refine(
    (data) => {
      if (data.application_start_date && data.application_end_date) {
        return data.application_end_date > data.application_start_date;
      }
      return true;
    },
    {
      message: 'Application deadline must be after starting date',
      path: ['application_end_date'],
    }
  );

export type JobInfoType = z.infer<typeof jobInfoSchema>;

export default jobInfoSchema;
