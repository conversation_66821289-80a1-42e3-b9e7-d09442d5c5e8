'use client';

import HookFormItem from '@/components/hook-form/HookFormItem';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useLoginMutation } from '@/hooks/api/use-auth';
import { useAuthSlice } from '@/hooks/use-auth-slice';
import { getCapitalizedText } from '@/utils/helper';
import adminLoginSchema, { type AdminLoginType } from '@/validations/adminLoginSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate } from '@tanstack/react-router';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

const LoginForm = () => {
  const navigate = useNavigate();
  // const searchParam = useSearchParams();

  const { setAuth, setTokens } = useAuthSlice();

  const { mutate: login, isPending } = useLoginMutation();
  const form = useForm<AdminLoginType>({
    resolver: zodResolver(adminLoginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = (data: AdminLoginType) => {
    login(data, {
      onSuccess: (response) => {
        toast.success(getCapitalizedText(response?.message));
        form.reset();
        setAuth(response);
        setTokens(response.access_token, response.refresh_token);
        // localStorage.setItem("previa-user-data", JSON.stringify(response.data.data));
        // if (searchParam.has('redirect')) {
        //   const redirectUrl = searchParam.get('redirect');

        //   if (!redirectUrl) {
        //     navigate({ to: '/dashboard' });
        //     return;
        //   }

        //   if (redirectUrl === '/login') {
        //     navigate({ to: '/dashboard' });
        //     return;
        //   }
        //   navigate({ to: redirectUrl });
        //   return;
        // }
        navigate({ to: '/admin/dashboard' });
      },
      onError: () => {
        toast.error('Login failed. Please check your credentials.');
      },
    });
  };

  return (
    <Card className='p-14 shadow-none'>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardContent className='mb-6 space-y-4'>
            <HookFormItem name='email' label='Email'>
              <Input placeholder='Enter your email address' className='' />
            </HookFormItem>

            <HookFormItem name='password' label='Password'>
              <Input type='password' placeholder='Enter your password' />
            </HookFormItem>

            {/* <p className='text-primary-600 mb-2 text-sm'>Forgot password?</p> */}
          </CardContent>

          <CardFooter>
            <Button
              type='submit'
              className='w-full rounded-full bg-gradient-to-r from-[#3B81F6] to-[#2664EB] text-white'
              loading={isPending}
            >
              Log in
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
};
export default LoginForm;
