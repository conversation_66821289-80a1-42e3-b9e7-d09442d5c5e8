import { Button } from '@/components/ui/button';
import { useJobCreationStore } from '@/stores/job-creation-store';
import { IconPlus } from '@tabler/icons-react';
import { useNavigate } from '@tanstack/react-router';

export function JobPrimaryButtons() {
  const navigate = useNavigate();
  const { clearDraftJobId } = useJobCreationStore();

  const handleNewJobPost = () => {
    clearDraftJobId();
    navigate({ to: '/admin/jobs/create' });
  };

  return (
    <div className='flex gap-2'>
      <Button className='space-x-1' onClick={handleNewJobPost}>
        <span>Add New Job Post</span> <IconPlus size={18} />
      </Button>
    </div>
  );
}
