'use client';

import { JOB_STATUS } from '../data/constant';
import type { JobListItem } from '../data/job-list-schema';
import { AnimatedTabs, TabConfig } from '@/components/ui/animated-tabs';

interface JobTabsProps {
  data: JobListItem[];
  onTabChange?: (tab: string) => void;
}

export function JobTabs({ data, onTabChange }: JobTabsProps) {
  const statusValues = Object.values(JOB_STATUS);

  const counts = statusValues?.reduce(
    (acc, status) => {
      acc[status] = data?.filter((job) => job?.status === status)?.length;
      return acc;
    },
    {} as Record<string, number>
  );

  counts.all = data.length;

  const tabConfig: TabConfig[] = [
    ...statusValues.map((status) => ({
      id: status,
      label: status?.charAt(0)?.toUpperCase() + status?.slice(1),
      count: counts[status],
    })),
    { id: 'all', label: 'All Jobs', count: counts.all },
  ];

  return (
    <AnimatedTabs tabs={tabConfig} defaultTab='all' onTabChange={onTabChange} showCounts={true} />
  );
}
