import { callTypes } from '../data/constant';
import { JobListItem } from '../data/job-list-schema';
import { DataTableColumnHeader } from './data-table-column-header';
import { DataTableRowActions } from './data-table-row-actions';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { getDaysLeft } from '@/utils/helper';
import { ColumnDef } from '@tanstack/react-table';

export const columns: ColumnDef<JobListItem>[] = [
  {
    accessorKey: 'title',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Job Title' />,
    cell: ({ row }) => <div className='font-medium'>{row.getValue('title')}</div>,
  },
  {
    accessorKey: 'location',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Location' />,
    cell: ({ row }) => <div>{row.getValue('location') || 'N/A'}</div>,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
    enableSorting: false,
  },
  {
    id: 'experience',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Experience' />,
    cell: ({ row }) => {
      const { min_exp, max_exp } = row.original;
      const hasMinExp = min_exp !== null && min_exp !== undefined;
      const hasMaxExp = max_exp !== null && max_exp !== undefined;
      if (hasMinExp && hasMaxExp) {
        return (
          <div>
            {min_exp}-{max_exp} years
          </div>
        );
      }
      return <div>N/A</div>;
    },
  },
  {
    accessorKey: 'application_period',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Deadline' />,
    cell: ({ row }) => {
      const { application_start_date, application_end_date } = row.original;
      const daysLeft = getDaysLeft(application_start_date, application_end_date, 'd');
      return <div>{daysLeft}</div>;
    },
    enableHiding: false,
  },
  {
    accessorKey: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Status' />,
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      const badgeColor = callTypes.get(status);

      return (
        <Badge variant='outline' className={cn('capitalize', badgeColor)}>
          {status}
        </Badge>
      );
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Actions' />,
    cell: DataTableRowActions,
  },
];
