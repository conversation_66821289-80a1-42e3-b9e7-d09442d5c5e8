export const JOB_STATUS = {
  PUBLISHED: 'published',
  SCHEDULED: 'scheduled',
  DRAFT: 'draft',
  INACTIVE: 'inactive',
} as const;

export const { PUBLISHED, SCHEDULED, DRAFT, INACTIVE } = JOB_STATUS;

// Type for job status
export type JobStatus = (typeof JOB_STATUS)[keyof typeof JOB_STATUS];

// Status color mappings
export const callTypes = new Map<string, string>([
  [PUBLISHED, 'bg-teal-100/90 text-teal-900 dark:text-teal-900 border-teal-200'],
  [SCHEDULED, 'bg-neutral-300/40 border-neutral-300'],
  [DRAFT, 'bg-sky-200/40 text-sky-900 dark:text-sky-100 border-sky-300'],
  [
    INACTIVE,
    'bg-destructive/30 dark:bg-destructive/50 text-destructive dark:text-primary border-destructive/10',
  ],
]);
