import { PU<PERSON>ISHED, SCHEDULED, DRAFT, INACTIVE, type JobStatus } from './constant';
import { z } from 'zod';

export const jobStatusSchema = z.enum([PUBLISHED, SCHEDULED, DRAFT, INACTIVE]);

export type { JobStatus };

export const jobSchema = z.object({
  id: z.string(),
  title: z.string(),
  location: z.string().optional(),
  min_exp: z.number().optional(),
  max_exp: z.number().optional(),
  application_end_date: z.string().optional(),
  application_start_date: z.string().optional(),
  status: jobStatusSchema,
});

export type JobListItem = z.infer<typeof jobSchema>;

export const jobListSchema = z.array(jobSchema);
