import { JobListItem } from '../data/job-list-schema';
import React, { useState } from 'react';

interface JobsContextType {
  currentRow: JobListItem | null;
  setCurrentRow: React.Dispatch<React.SetStateAction<JobListItem | null>>;
}

const JobsContext = React.createContext<JobsContextType | null>(null);

interface Props {
  children: React.ReactNode;
}

export default function JobsProvider({ children }: Props) {
  const [currentRow, setCurrentRow] = useState<JobListItem | null>(null);

  return <JobsContext value={{ currentRow, setCurrentRow }}>{children}</JobsContext>;
}

export const useJobs = () => {
  const jobsContext = React.useContext(JobsContext);

  if (!jobsContext) {
    throw new Error('useJobs has to be used within <JobsContext>');
  }

  return jobsContext;
};
