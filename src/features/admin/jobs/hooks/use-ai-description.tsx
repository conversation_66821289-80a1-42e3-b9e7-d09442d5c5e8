import {
  useGenerateJobDescription as useGenerateJobDescriptionQuery,
  useUpdateJobMutation,
} from '@/hooks/api/use-job';
import { useState } from 'react';
import { toast } from 'sonner';

export const useAiDescription = (jobId?: string) => {
  const [isEnabled, setIsEnabled] = useState(false);
  const updateJobMutation = useUpdateJobMutation();

  const {
    data,
    isLoading,
    isRefetching,
    error,
    refetch,
  } = useGenerateJobDescriptionQuery(jobId || '', {
    enabled: isEnabled && !!jobId,
  });

  const parseHtmlContent = (rawContent: string): string => {
    if (!rawContent) return '';

    // Remove markdown code block syntax and clean up the content
    let cleanedContent = rawContent
      .replace(/```html\n?/g, '') // Remove opening ```html
      .replace(/```\n?/g, '') // Remove closing ```
      // .replace(/\\n/g, '\n') // Replace escaped newlines with actual newlines
      .replace(/\n/g, '') // Remove actual newlines to let HTML handle formatting
      .trim();

    cleanedContent = cleanedContent.replace(/•\s*•/g, '•');

    return cleanedContent;
  };

  const generateJobDescription = async (): Promise<string | null> => {
    if (!jobId) {
      toast.error('Job needs to be created to generate description');
      return null;
    }

    try {
      setIsEnabled(true);
      const result = await refetch();

      if (result.data) {
        const parsedContent = parseHtmlContent(result.data);
        // toast.success('Job description generated successfully');
        return parsedContent;
      } else {
        toast.error('Failed to generate job description');
        return null;
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'An error occurred while generating job description';
      toast.error(errorMessage);
      return null;
    } finally {
      setIsEnabled(false);
    }
  };

  const saveDescription = async (
    description: string,
    additionalPayload?: Partial<JobInformation>
  ) => {
    if (!jobId) {
      toast.error('Job ID is required for saving description');
      return false;
    }

    try {
      await updateJobMutation.mutateAsync({
        id: jobId,
        payload: {
          description,
          ...additionalPayload,
        } as JobInformation,
      });
      return true;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'An error occurred while saving job description';
      toast.error(errorMessage);
      return false;
    }
  };

  return {
    generateJobDescription,
    saveDescription,
    isSaving: updateJobMutation.isPending,
    isGenerating: isLoading || isRefetching,
    error,
    parsedDescription: data ? parseHtmlContent(data) : null,
  };
};
