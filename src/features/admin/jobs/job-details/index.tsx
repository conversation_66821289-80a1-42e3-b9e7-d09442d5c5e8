import { JobDescriptionContent } from './components/job-description-content';
import { JobDetailsHeader } from './components/job-details-header';
import { StatsGrid } from './components/stats-grid';
import { Main } from '@/components/layout/main';
import { PageHeader } from '@/components/shared/PageHeader';
import { AnimatedTabs } from '@/components/ui/animated-tabs';
import { useJobByIdQuery } from '@/hooks/api/use-job';
import { Route } from '@/routes/_authenticated/admin/jobs/$jobId';
import { getDaysLeft } from '@/utils/helper';
import { ErrorComponent, useLocation } from '@tanstack/react-router';
import { Award, Users, ThumbsUp, Clock, Timer, Calendar } from 'lucide-react';
import { useState, useEffect } from 'react';
import { JOB_STATUS } from '../job-list/data/constant';

const breadcrumbsItem = [
  { label: 'Home', href: '/' },
  { label: 'Job list', href: '/jobs' },
  { label: 'Job Details' },
];

export default function JobDetailsPage() {
  const { jobId } = Route.useParams();
  const { state } = useLocation();
  const jobFromState = state?.job;
  const [currentJob, setCurrentJob] = useState<JobList>(jobFromState);
  const [stats, setStats] = useState([
    {
      label: 'Experience',
      value: 'Loading...',
      cardIcon: Award,
      iconColor: 'bg-purple-400',
    },
    {
      label: 'Total Candidates',
      value: 'Loading...',
      cardIcon: Users,
      iconColor: 'bg-blue-400',
    },
    {
      label: 'Recommended',
      value: 'Loading...',
      cardIcon: ThumbsUp,
      iconColor: 'bg-green-400',
    },
    {
      label: 'Time left',
      value: 'Loading...',
      cardIcon: Clock,
      iconColor: 'bg-orange-400',
    },
    {
      label: 'Interview Duration',
      value: 'Loading...',
      cardIcon: Timer,
      iconColor: 'bg-red-400',
    },
    {
      label: 'Created at',
      value: 'Loading...',
      cardIcon: Calendar,
      iconColor: 'bg-gray-400',
    },
  ]);
  const tabsConfig = [
    { id: 'job-description', label: 'Job Description', count: 1 },
    { id: 'applicants', label: 'Applicants', count: 104 },
    { id: 'interview', label: 'Interview', count: 6 },
  ];

  const {
    data: jobData,
    isLoading,
    error,
  } = useJobByIdQuery(jobId, {
    enabled: !!jobId,
  });

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
      });
    } catch {
      return 'N/A';
    }
  };

  const getStatsValues = (jobData: JobList) => [
    jobData?.min_exp !== undefined && jobData?.max_exp !== undefined 
      ? `${jobData.min_exp}-${jobData.max_exp} Years` 
      : 'Not specified',
    '0', // Total Applicants - placeholder until we have this data from API
    '0', // Recommended - placeholder until we have this data from API
    jobData?.application_start_date && jobData?.application_end_date
      ? getDaysLeft(jobData.application_start_date, jobData.application_end_date)
      : 'Not specified',
    'Not specified', // Interview Duration - placeholder until we have this data from API
    jobData?.application_start_date ? formatDate(jobData.application_start_date) : 'Not specified',
  ];

  useEffect(() => {
    if (jobData) {
      setCurrentJob(jobData);
      const newValues = getStatsValues(jobData);
      setStats((prevStats) =>
        prevStats.map((stat, index) => ({ ...stat, value: newValues[index] }))
      );
    }
  }, [jobData]);

  const [activeTab, setActiveTab] = useState('job-description');

  const renderTabContent = () => {
    switch (activeTab) {
      case 'job-description':
        return <JobDescriptionContent description={currentJob.description} />;
      case 'applicants':
        return <div>Applicants content here</div>;
      case 'interview':
        return <div>Interview content here</div>;
      default:
        return <JobDescriptionContent description={currentJob.description} />;
    }
  };

  if (isLoading) {
    return (
      <div className='flex h-screen items-center justify-center'>
        <div className='border-primary size-8 animate-spin rounded-full border-b-2'></div>
      </div>
    );
  }

  if (error) return <ErrorComponent error={error} />;

  if (!currentJob) return <div className='p-4'>Job not found</div>;

  return (
    <div className='dark:bg-background flex h-full flex-col bg-neutral-100'>
      <PageHeader title={currentJob.title} showNavigation={true} breadcrumbs={breadcrumbsItem} />
      <Main className='px-8'>
        <JobDetailsHeader
          title={currentJob.title}
          status={currentJob.status || JOB_STATUS.INACTIVE}
          location={currentJob.location}
        />

        <StatsGrid stats={stats} />

        <div>
          <AnimatedTabs
            tabs={tabsConfig}
            defaultTab='job-description'
            onTabChange={setActiveTab}
            showCounts={true}
          />
          <div className='my-4'>{renderTabContent()}</div>
        </div>
      </Main>
    </div>
  );
}
