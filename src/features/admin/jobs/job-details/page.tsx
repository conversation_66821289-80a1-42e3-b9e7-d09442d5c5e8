import { DataTabs } from '@/components/data-tabs';

export const JobDetails = () => {
  const DEFAULT_TABS: TabItem[] = [
    {
      value: 'job_description',
      label: 'Job Description',
      content: () => (
        <div className='flex h-full flex-col p-6'>
          <div className='space-y-2'>
            <h3 className='text-2xl font-semibold tracking-tight'>Job Description</h3>
            <p className='text-muted-foreground text-sm leading-relaxed'>
              Choose the model you want to use
            </p>
          </div>
        </div>
      ),
    },
    {
      value: 'interview_agents',
      label: 'Interview Agents',
      content: () => (
        <div className='flex h-full flex-col p-6'>
          <div className='space-y-2'>
            <h3 className='text-xl font-semibold tracking-tight'>Interview Agents</h3>
            <p className='text-muted-foreground text-sm leading-relaxed'>
              Choose the MCP you want to use
            </p>
          </div>
        </div>
      ),
    },
    {
      value: 'total_applicants',
      label: 'Total Candidates',
      content: () => (
        <div className='flex h-full flex-col p-6'>
          <div className='space-y-2'>
            <h3 className='text-2xl font-semibold tracking-tight'>Total Candidates</h3>
            <p className='text-muted-foreground text-sm leading-relaxed'>
              Choose the agent you want to use
            </p>
          </div>
        </div>
      ),
    },
    {
      value: 'recommended_applicants',
      label: 'Recommended Applicants',
      content: () => (
        <div className='flex h-full flex-col p-6'>
          <div className='space-y-2'>
            <h3 className='text-2xl font-semibold tracking-tight'>Recommended Applicants</h3>
            <p className='text-muted-foreground text-sm leading-relaxed'>
              Choose the user you want to use
            </p>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className='p-6'>
      <DataTabs items={DEFAULT_TABS} />
    </div>
  );
};
