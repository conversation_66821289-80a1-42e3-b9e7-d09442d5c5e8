import { Tabs, TabsContent } from '@/components/ui/tabs';

interface JobDescriptionContentProps {
  description: string | undefined;
}

export function JobDescriptionContent({ description = '' }: JobDescriptionContentProps) {
  return (
    <Tabs defaultValue='job-description'>
      <TabsContent value='job-description' className='mt-8'>
        <div 
          className='space-y-4 prose prose-sm max-w-none dark:prose-invert'
          dangerouslySetInnerHTML={{ __html: description }}
        />
      </TabsContent>
    </Tabs>
  );
}
