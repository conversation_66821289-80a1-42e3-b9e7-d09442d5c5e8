import { Card, CardContent } from '@/components/ui';
import type { LucideIcon } from 'lucide-react';

interface StatsData {
  label: string;
  value: string;
  cardIcon: LucideIcon;
  iconColor: string;
}

interface StatsGridProps {
  stats: StatsData[];
}

export function StatsGrid({ stats }: StatsGridProps) {
  return (
    <div className='mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-6'>
      {stats.map((stat, index) => (
        <div key={index}>
          <Card className='bg-card rounded-md border border-gray-200 shadow-sm h-full'>
            <CardContent>
              <div className='flex items-start gap-3'>
                <div className={`rounded-full p-2 ${stat.iconColor}`}>
                  <stat.cardIcon className='h-4 w-4 text-white' />
                </div>
                <div className='flex-1'>
                  <p className='text-muted-foreground mb-1 text-sm'>{stat.label}</p>
                  <p className='text-foreground text-xl font-semibold'>{stat.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ))}
    </div>
  );
}
