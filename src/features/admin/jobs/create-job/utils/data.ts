export const durationOptions = [
  { value: '10', label: '10 minutes' },
  { value: '20', label: '20 minutes' },
  { value: '30', label: '30 minutes' },
  { value: '40', label: '40 minutes' },
  { value: '50', label: '50 minutes' },
  { value: '60', label: '60 minutes' },
];

export const sampleJobInformation = {
  id: '3f9dd0e7-a698-4ea0-b3c4-39dac3192291',
  title: 'Software Engineer',
  description:
    '<h2>Job Title: Software Engineer</h2><p><strong>Location:</strong> Dhaka</p><p><strong>Employment Type:</strong> Full-time</p><p><strong>Experience Level:</strong> Entry to Senior-Level (0–10 Years)</p><h3>About the Role</h3><p>This is a Demo Interview scenario, designed to help you experience how Previa’s AI-powered interview process works.</p><p>For this demo, the role is set as a Software Engineer (Sample Position). You will go through a simulated interview journey where questions are based on responsibilities and skills typically expected in this role.</p><h3>Key Responsibilities</h3><ul><li>Develop, test, and maintain web and/or mobile applications according to requirements.</li><li>Write clean, efficient, and maintainable code following best practices.</li><li>Collaborate with product and design teams to translate requirements into technical solutions.</li><li>Participate in code reviews, debugging, and troubleshooting to ensure product quality.</li><li>Assist in integrating APIs and third-party services as needed.</li><li>Stay updated with emerging technologies, frameworks, and industry trends.</li><li>Contribute to continuous improvement in development processes and team collaboration.</li></ul><h3>Requirements</h3><ul><li>Bachelor’s degree in Computer Science, Software Engineering, or a related field.</li><li>0–10 years of experience in software development</li><li>Proficiency in at least one programming language (e.g., JavaScript, Python, Java, C#).</li><li>Basic understanding of databases (SQL/NoSQL) and version control systems (Git).</li><li>Familiarity with web technologies such as HTML, CSS, and JavaScript frameworks.</li><li>Strong problem-solving skills and a willingness to learn new technologies.</li><li>Ability to work effectively in a team and manage tasks independently.</li></ul><h3>Important Note</h3><p>This JD is only for demonstration purposes. There is no real hiring process for this role. The goal is to help you experience how Previa’s AI interview evaluates candidates’ skills in a realistic setting.</p>',
  location: 'Dhaka, Bangladesh',
  min_exp: 0,
  max_exp: 10,
  status: 'published',
  application_start_date: '2025-08-20T09:00:00',
  application_end_date: '2025-09-20T23:59:59',
  eligibility_settings: {
    inclusive_requirements: 'Must have a degree in Computer Science',
    exclusive_requirements: 'Cannot have any criminal records',
  },
  position_applied_for: 'Software Engineer',
  skills: ['javascript', 'react', 'nodejs'],
};
