import { useJobStore } from '../../hooks/useJobStore';
import { durationOptions } from '../../utils/data';
import { calculateTotalDuration } from '../../utils/helper';
import { RobotIcon } from '@/assets/icons';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { sleep } from '@/utils/helper';
import { Reorder, useDragControls, useMotionValue } from 'framer-motion';
import { Hourglass, MoreHorizontal, SquarePen, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface DraggablePersonaCardProps {
  persona: InterviewPersona;
  setPersonas: React.Dispatch<React.SetStateAction<InterviewPersona[]>>;
}

export function DraggablePersonaCard({ persona, setPersonas }: DraggablePersonaCardProps) {
  const [isUpdateDuration, setIsUpdateDuration] = useState(false);

  const dragControls = useDragControls();
  const y = useMotionValue(0);
  const [isDragging, setIsDragging] = useState(false);

  const { formData, updateFormData } = useJobStore();

  const handleRemovePersona = () => {
    const updatedPersonas = formData.interviewPersonas.filter((p) => p.id !== persona.id);
    updateFormData({
      interviewPersonas: updatedPersonas,
      totalPersonas: updatedPersonas.length,
      totalDuration: calculateTotalDuration(updatedPersonas),
    });
    setPersonas(updatedPersonas);
  };

  return (
    <Reorder.Item
      layout
      layoutId={persona.id}
      initial={isDragging ? false : { opacity: 0, y: -20 }}
      animate={isDragging ? false : { opacity: 1, y: 0 }}
      exit={isDragging ? false : { opacity: 0, y: 20 }}
      value={persona}
      className='border-strock rounded-2xl border bg-white p-6'
      dragListener={false}
      dragControls={dragControls}
      style={{ y }}
    >
      <div className='flex items-center gap-2'>
        <div
          className='-ml-3 cursor-grab touch-none rounded-lg p-3 transition-colors select-none hover:bg-gray-50 active:cursor-grabbing'
          onPointerDown={(e) => {
            dragControls.start(e);
            setIsDragging(true);
          }}
          onPointerUp={(e) => {
            e.currentTarget.releasePointerCapture(e.pointerId);
            setIsDragging(false);
          }}
          style={{ touchAction: 'none' }}
        >
          <div className='grid grid-cols-2 gap-1'>
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className='bg-gray-light h-1 w-1 rounded-full transition-colors group-hover:bg-gray-500'
              />
            ))}
          </div>
        </div>

        <div className='bg-custom-white mr-2 rounded-md p-3'>
          <RobotIcon className='text-gray-dark' />
        </div>

        <div className='flex-1'>
          <h3 className='mb-1 text-lg font-semibold text-black'>{persona.persona}</h3>
          <div className='text-gray-light flex items-center text-sm'>
            Interview Duration:{' '}
            {isUpdateDuration ? (
              <Select
                value={persona.duration}
                onValueChange={(value) => {
                  const updatedPersonas = formData.interviewPersonas.map((p) =>
                    p.id === persona.id ? { ...p, duration: value } : p
                  );
                  updateFormData({
                    interviewPersonas: updatedPersonas,
                    totalDuration: calculateTotalDuration(updatedPersonas),
                  });
                  setPersonas(updatedPersonas);
                  setIsUpdateDuration(false);
                }}
                onOpenChange={async (open) => {
                  if (!open && isUpdateDuration) {
                    await sleep(100);
                    setIsUpdateDuration(false);
                  }
                }}
              >
                <SelectTrigger className='!h-10 w-40 border-none shadow-none'>
                  <SelectValue placeholder='Select Duration' />
                </SelectTrigger>
                <SelectContent>
                  {durationOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <span className='text-gray-dark ml-1 font-medium'>{persona.duration} minutes</span>
            )}
          </div>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
              <MoreHorizontal className='text-gray-dark h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end' className='text-gray-dark'>
            <DropdownMenuItem>
              <Hourglass className='text-gray-dark size-4' />
              <p onClick={() => setIsUpdateDuration((prev) => !prev)}>
                {isUpdateDuration ? 'Cancel Update' : 'Update Duration'}
              </p>
            </DropdownMenuItem>
            <DropdownMenuItem disabled>
              <SquarePen className='text-gray-dark size-4' />
              <p>Edit Persona</p>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleRemovePersona}>
              <Trash2 className='text-gray-dark size-4' />
              <p>Remove</p>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </Reorder.Item>
  );
}
