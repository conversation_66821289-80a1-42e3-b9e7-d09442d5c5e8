import { Switch, Label, Textarea } from '@/components/ui';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Info } from 'lucide-react';
import { useEffect, useState } from 'react';

interface EligibilitySettingsProps {
  onEligibilityChange?: (data: { enabled: boolean; initialCriteria: string | null }) => void;
  initialData?: {
    enabled: boolean;
    initialCriteria: string | null;
  };
}

export function EligibilitySettings({
  onEligibilityChange,
  initialData,
}: EligibilitySettingsProps) {
  const [eligibilityEnabled, setEligibilityEnabled] = useState(initialData?.enabled || false);
  const [initialCriteria, setInitialCriteria] = useState(initialData?.initialCriteria || '');

  useEffect(() => {
    setEligibilityEnabled(initialData?.enabled || false);
    setInitialCriteria(initialData?.initialCriteria || '');
  }, [initialData]);

  const handleToggleChange = (enabled: boolean) => {
    setEligibilityEnabled(enabled);
    onEligibilityChange?.({
      enabled,
      initialCriteria,
    });
  };

  const handleInclusiveChange = (value: string) => {
    setInitialCriteria(value);
    onEligibilityChange?.({
      enabled: eligibilityEnabled,
      initialCriteria: value,
    });
  };

  return (
    <div className='space-y-4'>
      {/* Toggle Section */}
      <div className='bg-custom-white flex flex-col items-center justify-between rounded-lg p-4'>
        <div className='flex w-full justify-between'>
          <div className='flex items-center gap-2'>
            <Label htmlFor='eligibility-toggle' className='text-sm font-medium'>
              Want to set Candidate Eligibility Settings
            </Label>
            <Tooltip>
              <TooltipTrigger className='pointer-events-auto'>
                <Info className='text-muted-foreground pointer-events-none size-4 cursor-pointer' />
              </TooltipTrigger>
              <TooltipContent>
                AI filters CVs against your set criteria, ensuring only qualified candidates move
                forward.
              </TooltipContent>
            </Tooltip>
          </div>
          <Switch
            id='eligibility-toggle'
            checked={eligibilityEnabled}
            onCheckedChange={handleToggleChange}
          />
        </div>
        {/* Eligibility Requirements Section */}
        <div className='flex w-full'>
          {eligibilityEnabled && (
            <div className='animate-in slide-in-from-top-2 mt-6 w-full space-y-4 duration-200'>
              <div className='space-y-2'>
                <Label
                  htmlFor='inclusive-requirements'
                  className='text-foreground text-sm font-medium'
                >
                  Inclusive and Exclusive Requirements
                </Label>
                <Textarea
                  id='inclusive-requirements'
                  placeholder='Enter requirements that candidates must have or requirements that would disqualify candidates...'
                  value={initialCriteria}
                  onChange={(e) => handleInclusiveChange(e.target.value)}
                  className='bg-card min-h-[80px]'
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
