import { useAiDescription } from '../../hooks/use-ai-description';
import { JOB_STATUS } from '../../job-list/data/constant';
import { AI, EditorMode, MANUAL } from '../data/constant';
import { EligibilitySettings } from './eligibility-settings';
import { JobDescriptionEditor } from './job-description-editor';
import HookFormItem from '@/components/hook-form/HookFormItem';
import SimpleSelect from '@/components/select/simple-selector';
import {
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  DatePicker,
  Form,
  Input,
} from '@/components/ui';
import { useJobStore } from '@/features/admin/jobs/create-job/hooks/useJobStore';
import {
  useCreateJobMutation,
  useJobByIdQuery,
  useUpdateJobMutation,
  useJobRolesQuery,
} from '@/hooks/api/use-job';
import { useJobCreationStore } from '@/stores/job-creation-store';
import jobInfoSchema, { type JobInfoType } from '@/validations/jobSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';
import { forwardRef, useEffect, useImperativeHandle, useState, useMemo } from 'react';
import { useForm } from 'react-hook-form';

export const JobDescriptionStep = forwardRef<
  { triggerValidation: () => Promise<boolean> },
  { showEditor: boolean; setShowEditor: (value: boolean) => void }
>(({ showEditor, setShowEditor }, ref) => {
  const allLabelClsName = '!text-foreground font-semibold tracking-wide';
  const { updateFormData } = useJobStore();
  const { draftJobId, setDraftJobId } = useJobCreationStore();
  const { mutate: createJobInformation, isPending: isCreating } = useCreateJobMutation();
  const { mutate: updateJobInformation, isPending: isUpdating } = useUpdateJobMutation();
  const [jobId, setJobId] = useState<string | undefined>(undefined);
  const [editorMode, setEditorMode] = useState<EditorMode>(MANUAL);
  const [generatedDescription, setGeneratedDescription] = useState<string>('');
  const [eligibilityData, setEligibilityData] = useState<{
    enabled: boolean;
    initialCriteria: string | null;
  }>({
    enabled: false,
    initialCriteria: null,
  });

  const { data: draftJobData, isLoading: isDraftLoading } = useJobByIdQuery(draftJobId || '', {
    enabled: !!draftJobId,
  });

  const { data: jobRolesData, isLoading: isJobRolesLoading } = useJobRolesQuery();

  const { generateJobDescription, isGenerating } = useAiDescription(
    jobId || draftJobId || undefined
  );

  // Transform job roles data for Select component
  const jobRoleOptions = useMemo(() => {
    if (!jobRolesData) return [];
    return jobRolesData.map((role: JobRole) => ({
      label: role.name,
      value: role.id,
    }));
  }, [jobRolesData]);

  const form = useForm<JobInfoType>({
    resolver: zodResolver(jobInfoSchema),
    defaultValues: {
      title: '',
      description: '',
      job_role_id: '',
      min_exp: '',
      max_exp: '',
      location: '',
      // skills: [],
    },
  });

  const isProcessing = isCreating || isUpdating;
  const isDraftMode = !!draftJobData;

  useEffect(() => {
    if (draftJobData && !isDraftLoading && jobRoleOptions.length > 0 && !isJobRolesLoading) {
      const applicationStartDate = draftJobData.application_start_date
        ? new Date(draftJobData.application_start_date)
        : undefined;
      const applicationEndDate = draftJobData.application_end_date
        ? new Date(draftJobData.application_end_date)
        : undefined;

      console.log("")
      form.reset({
        title: draftJobData.title || '',
        description: draftJobData.description || '',
        job_role_id: draftJobData.job_role?.id || '',
        location: draftJobData.location || '',
        min_exp: draftJobData.min_exp?.toString() || '',
        max_exp: draftJobData.max_exp?.toString() || '',
        application_start_date: applicationStartDate,
        application_end_date: applicationEndDate,
      });

      setEligibilityData({
        enabled: !!draftJobData.initial_filter_criteria,
        initialCriteria: draftJobData.initial_filter_criteria || null,
      });

      setJobId(draftJobData.id);

      if (draftJobData.description) {
        setGeneratedDescription(draftJobData.description);
      }
    }
  }, [draftJobData, jobRoleOptions]);

  useImperativeHandle(ref, () => ({
    triggerValidation: async () => {
      return await form.trigger();
    },
  }));
  const handleGenerateWithAI = async () => {
    if (isGenerating) return;
    const generatedDescription = await generateJobDescription();

    if (generatedDescription) {
      setGeneratedDescription(generatedDescription);
    }
  };

  const onSubmit = (data: JobInfoType, submitType: EditorMode) => {
    const jobData: JobInformation = {
      ...data,
      // skills: getValuesFromOptions(data.skills),
      min_exp: parseInt(data.min_exp),
      max_exp: parseInt(data.max_exp),
      location: data.location || 'Dhaka',
      application_start_date: data.application_start_date?.toISOString(),
      application_end_date: data.application_end_date?.toISOString(),
      status: JOB_STATUS.DRAFT,
      initial_filter_criteria: eligibilityData.enabled ? eligibilityData.initialCriteria : null,
    };

    updateFormData(jobData);
    setEditorMode(submitType);

    // Use update if we have a draft job, otherwise create new

    if (draftJobId && jobId) {
      updateJobInformation(
        { id: jobId, payload: jobData },
        {
          onSuccess: async (response) => {
            setShowEditor(true);
            if (submitType === AI) {
              await handleGenerateWithAI();
            }
            setJobId(response?.id || jobId);
          },
        }
      );
    } else {
      createJobInformation(jobData, {
        onSuccess: async (response) => {
          if (submitType === AI) {
            await handleGenerateWithAI();
          }
          setJobId(response?.id);
          setDraftJobId(response?.id);
          setShowEditor(true);
        },
      });
    }
  };

  const handleEligibilityChange = (data: { enabled: boolean; initialCriteria: string | null }) => {
    setEligibilityData(data);
  };

  if (showEditor) {
    return (
      <JobDescriptionEditor
        mode={editorMode}
        jobId={jobId}
        initialDescription={generatedDescription}
        onRegenerateAI={handleGenerateWithAI}
        isGenerating={isGenerating}
      />
    );
  }

  if (isDraftLoading) {
    return (
      <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className='mx-auto max-w-2xl'>
        <Card className='border-none bg-transparent shadow-none'>
          <CardContent className='flex items-center justify-center p-8'>
            <div className='text-center'>
              <div className='border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2'></div>
              <p className='text-muted-foreground'>Loading draft job...</p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  console.log('form values', form.getValues()?.job_role_id);

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      className='mx-auto max-w-2xl'
    >
      <Card className='border-none bg-transparent shadow-none'>
        <CardHeader className='text-center'>
          <CardTitle className='text-xl font-bold'>
            {isDraftMode ? 'Edit Draft Job' : 'Define the Role'}
          </CardTitle>
          <CardDescription className='text-md text-muted-foreground mx-auto max-w-xl'>
            {isDraftMode
              ? 'Continue editing your draft job or update the description with AI'
              : 'Create your job post your way. Write a prompt to let AI generate it for you, or build it manually by writing by your own'}
          </CardDescription>
        </CardHeader>
        <Form {...form}>
          <form>
            <CardContent className='bg-card z-[9999] space-y-6 rounded-2xl border border-neutral-200 p-10'>
              <div className='space-y-2'>
                <HookFormItem name='title' label='Job title' labelClassName={allLabelClsName}>
                  <Input placeholder='e.g. Senior React Developer' />
                </HookFormItem>
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <HookFormItem
                    name='job_role_id'
                    label='Position Applied For'
                    labelClassName={allLabelClsName}
                  >
                    <SimpleSelect
                      options={jobRoleOptions}
                      placeholder='Select job position'
                      name='job_role_id'
                      defaultValue={form.watch('job_role_id')}
                      // value={form.watch('job_role_id')}
                    />
                  </HookFormItem>
                </div>

                <div className='space-y-2'>
                  <HookFormItem name='location' label='Location' labelClassName={allLabelClsName}>
                    <Input placeholder='Enter location' name='location' />
                  </HookFormItem>
                </div>
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <HookFormItem
                    name='min_exp'
                    label='Min Experience (Years)'
                    labelClassName={allLabelClsName}
                  >
                    <Input />
                  </HookFormItem>
                </div>

                <div className='space-y-2'>
                  <HookFormItem
                    name='max_exp'
                    label='Max Experience (Years)'
                    labelClassName={allLabelClsName}
                  >
                    <Input />
                  </HookFormItem>
                </div>
              </div>

              {/* Skills Section if needed */}
              {/* <div className='space-y-2'>
                  <HookFormItem name='skills' label='Skills' labelClassName={allLabelClsName}>
                    <MultiSelect options={skills} placeholder='Select skills' />
                  </HookFormItem>
                </div> */}

              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <HookFormItem
                    name='application_start_date'
                    label='Starting date'
                    className='flex flex-col'
                    labelClassName={allLabelClsName}
                  >
                    <DatePicker
                      key='start-date'
                      id='start-date'
                      defaultDate={form.watch('application_start_date')}
                      onChange={(date) => {
                        if (date) form.setValue('application_start_date', date);
                      }}
                    />
                  </HookFormItem>
                </div>

                <div className='space-y-2'>
                  <HookFormItem
                    name='application_end_date'
                    label='Ending date'
                    className='flex flex-col'
                    labelClassName={allLabelClsName}
                  >
                    <DatePicker
                      key='end-date'
                      id='end-date'
                      defaultDate={form.watch('application_end_date')}
                      onChange={(date) => {
                        if (date) form.setValue('application_end_date', date);
                      }}
                    />
                  </HookFormItem>
                </div>
              </div>

              <EligibilitySettings
                onEligibilityChange={handleEligibilityChange}
                initialData={eligibilityData}
              />

              <div className='flex w-full gap-3'>
                <div className='relative inline-block w-1/2'>
                  <div className='absolute -inset-0.5 overflow-hidden rounded-lg'>
                    <div className='animate-border-sweep absolute inset-0 bg-gradient-to-r from-transparent via-blue-500 to-transparent opacity-80'></div>
                  </div>

                  <Button
                    type='submit'
                    name='submitTypeAi'
                    value={AI}
                    disabled={isProcessing}
                    onClick={form.handleSubmit((data) => onSubmit(data, AI))}
                    className='bg-logo-gradient relative w-full min-w-[250px] overflow-hidden border-0 transition-all before:absolute before:top-0 before:right-0 before:h-full before:w-12 before:translate-x-12 before:rotate-6 before:bg-white before:opacity-10 before:duration-700 hover:shadow-lg hover:shadow-purple-500/30 hover:before:-translate-x-80'
                  >
                    <span className='relative z-10'>Create Description with AI</span>
                  </Button>
                </div>

                <Button
                  variant='secondary'
                  type='submit'
                  name='submitTypeManual'
                  value={MANUAL}
                  onClick={form.handleSubmit((data) => onSubmit(data, MANUAL))}
                  disabled={isProcessing}
                  className='w-1/2'
                >
                  Create manually
                </Button>
              </div>
            </CardContent>
          </form>
        </Form>
      </Card>
    </motion.div>
  );
});
