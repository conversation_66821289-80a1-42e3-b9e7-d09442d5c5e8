import { ApiServiceInstance, CandidateApiServiceInstance } from '.';
import { API_ROUTES } from '@/config/api';

const { CREATE, GET_ALL, GET_JOB_ROLES, GET_BY_ID, GET_DETAILS, GET_ACTIVE_JOBS, GET_GENERATE_DESCRIPTION } = API_ROUTES.JOB;
export const createJob = async (
  payload: JobInformation
): Promise<IResponseData<JobInformationResponse>> => {
  return ApiServiceInstance.callPostApi<JobInformationResponse, JobInformation>(CREATE, payload);
};

export const getGenerateJobDescription = async (
  jobId: string
): Promise<IResponseData<string>> => {
  return ApiServiceInstance.callGetApi(GET_GENERATE_DESCRIPTION(jobId));
};

export const getJobs = async (): Promise<IResponseData<JobList[]>> => {
  return ApiServiceInstance.callGetApi<JobList[]>(GET_ALL);
};

export const getJobById = async (id: string): Promise<IResponseData<JobInformationResponse>> => {
  return ApiServiceInstance.callGetApi<JobInformationResponse>(GET_BY_ID(id));
};

export const getActiveJobs = async (): Promise<IResponseData<ActiveJob[]>> => {
  return CandidateApiServiceInstance.callGetApi<ActiveJob[]>(GET_ACTIVE_JOBS);
};

export const getJob = async (id: string): Promise<IResponseData<JobInformationResponse>> => {
  return CandidateApiServiceInstance.callGetApi<JobInformationResponse>(GET_DETAILS(id));
};

export const updateJob = async (
  id: string,
  payload: JobInformation
): Promise<IResponseData<JobInformationResponse>> => {
  return ApiServiceInstance.callPatchApi<JobInformationResponse, JobInformation>(GET_BY_ID(id), payload);
};

export const getJobRoles = async (): Promise<IResponseData<JobRole[]>> => {
  return ApiServiceInstance.callGetApi<JobRole[]>(GET_JOB_ROLES);
};
