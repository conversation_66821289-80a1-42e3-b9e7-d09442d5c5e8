import { ApiServiceInstance } from '.';
import { API_ROUTES } from '@/config/api';

const { GET_ALL, ATTACH_TO_JOB } = API_ROUTES.PERSONA;

export const getPersonas = async (): Promise<IResponseData<Persona[]>> => {
  return ApiServiceInstance.callGetApi<Persona[]>(GET_ALL);
};

export const attachPersonaToJob = async (payload: {
  jobId: string;
  list: { personas: JobPersona[] };
}): Promise<IResponseData<unknown>> => {
  return ApiServiceInstance.callPostApi<unknown, JobPersona[]>(
    ATTACH_TO_JOB(payload.jobId),
    payload.list.personas
  );
};
